# React Router Fix for XBlog Frontend
# This file should be placed in the public_html folder of your Hostinger hosting

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle React Router - redirect all requests to index.html
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache Control for Static Assets
<IfModule mod_expires.c>
    ExpiresActive on
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    
    # Images
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML (shorter cache for dynamic content)
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Add this to your existing .htaccess file
<IfModule mod_headers.c>
    # Prevent caching of HTML files
    <FilesMatch "\.(html)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
    
    # Force revalidation of JS/CSS files
    <FilesMatch "\.(js|css)$">
        Header set Cache-Control "public, max-age=31536000, must-revalidate"
        Header set ETag ""
    </FilesMatch>
</IfModule>

# Add version parameter to force reload
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Add version to static assets
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.+)\.(js|css)$ /$1.$2?v=%{TIME_STAMP} [L]
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to source maps in production
<Files ~ "\.map$">
    Order allow,deny
    Deny from all
</Files>
